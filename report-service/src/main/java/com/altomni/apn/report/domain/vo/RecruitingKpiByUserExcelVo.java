package com.altomni.apn.report.domain.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.altomni.apn.common.dto.recruiting.RecruitingKpiReportSearchDto;
import com.altomni.apn.common.vo.recruiting.RecruitingKpiByUserVO;
import lombok.Getter;
import lombok.Setter;

import java.util.Optional;

@Getter
@Setter
public class RecruitingKpiByUserExcelVo {

    @ExcelProperty(value = "Team")
    private String team;

    @ExcelProperty(value = "User")
    private String user;

    @ExcelProperty(value = "Date")
    private String groupByDate;

    @ExcelProperty(value = "Sum of Openings")
    private String openings;

    @ExcelProperty(value = "Created Companies")
    private String createdCompaniesNum;

    @ExcelProperty(value = "Upgrade To Client")
    private String upgradeToClient;

    @ExcelProperty(value = "Created Candidates")
    private String talentNum;

    @ExcelProperty(value = "Submitted to Job")
    private String submitToJobNum;

    @ExcelProperty(value = "Submitted to Client")
    private String submitToClientNum;

    @ExcelProperty(value = "Interview Appointments")
    private String interviewAppointments;

    @ExcelProperty(value = "Interview (First Round)")
    private String firstInterviewNum;

    @ExcelProperty(value = "Interview (Second Round)")
    private String secondInterviewNum;

    @ExcelProperty(value = "Two or More Interviews")
    private String twoOrMoreInterviews;

    @ExcelProperty(value = "Interview (Final Round)")
    private String finalInterviewNum;

    @ExcelProperty(value = "Sum of Interview")
    private String interviewNum;

    @ExcelProperty(value = "Offered")
    private String offerNum;

    @ExcelProperty(value = "Offer Accetped")
    private String offerAcceptNum;

    @ExcelProperty(value = "On Boarded")
    private String onboardNum;

    @ExcelProperty(value = "Eliminated")
    private String eliminateNum;

    @ExcelProperty(value = "AI Candidate Interview Conversion Rate")
    private String aiCandidateInterviewConversionRate;

    @ExcelProperty(value = "AI Candidate Onboarding Rate")
    private String aiCandidateOnboardingRate;

    @ExcelProperty(value = "Notes (Initial Candidate Interview)")
    private String iciNum;

    @ExcelProperty(value = "Notes (Call)")
    private String callNoteNum;

    @ExcelProperty(value = "Notes (Email)")
    private String emailNoteNum;

    @ExcelProperty(value = "Notes (In Person)")
    private String personNoteNum;

    @ExcelProperty(value = "Notes (Video Call)")
    private String videoNoteNum;

    @ExcelProperty(value = "Notes (Others)")
    private String otherNoteNum;

    @ExcelProperty(value = "Notes (Pipeline)")
    private String pipelineNoteNum;

    @ExcelProperty(value = "Notes (APN Pro)")
    private String apnProNoteNum;

    public static RecruitingKpiByUserExcelVo ofSearchResult(RecruitingKpiByUserVO vo, RecruitingKpiReportSearchDto searchDto) {
        RecruitingKpiByUserExcelVo excelVo = new RecruitingKpiByUserExcelVo();
        excelVo.setOpenings(safeLong(vo.getOpenings()));
        excelVo.setCreatedCompaniesNum(safeLong(vo.getCreatedCompaniesNum()));
        excelVo.setUpgradeToClient(safeLong(vo.getUpgradeToClient()));
        excelVo.setTalentNum(safeLong(vo.getTalentNum()));
        excelVo.setIciNum(safeLong(vo.getIciNum()));
        excelVo.setCallNoteNum(safeLong(vo.getCallNoteNum()));
        excelVo.setEmailNoteNum(safeLong(vo.getEmailNoteNum()));
        excelVo.setPersonNoteNum(safeLong(vo.getPersonNoteNum()));
        excelVo.setVideoNoteNum(safeLong(vo.getVideoNoteNum()));
        excelVo.setOtherNoteNum(safeLong(vo.getOtherNoteNum()));
        excelVo.setPipelineNoteNum(safeLong(vo.getPipelineNoteNum()));
        excelVo.setApnProNoteNum(safeLong(vo.getApnProNoteNum()));
        // 应用统计是精准推荐
        switch (searchDto.getApplicationStatusType()) {
            // 漏斗状态
            case ALL -> {
                if (searchDto.getAiTalentType() == null) {
                    excelVo.setSubmitToJobNum(safeLong(vo.getSubmitToJobNum()));
                    break;
                }
                switch (searchDto.getAiTalentType()) {
                    case TALENT_COUNT -> excelVo.setSubmitToJobNum("%s（%s）".formatted(safeLong(vo.getSubmitToJobNum()), vo.getSubmitToJobNumAIRecommend()));
                    case APPLICATION_COUNT -> excelVo.setSubmitToJobNum("%s（%s）".formatted(safeLong(vo.getSubmitToJobNum()), vo.getSubmitToJobNumPrecisionAIRecommend()));
                }
            }
            // 当前状态
            case CURRENT -> {
                if (searchDto.getAiTalentType() == null) {
                    excelVo.setSubmitToJobNum(safeLong(vo.getSubmitToJobCurrentNum()));
                    break;
                }
                switch (searchDto.getAiTalentType()) {
                    case TALENT_COUNT -> excelVo.setSubmitToJobNum("%s（%s）".formatted(safeLong(vo.getSubmitToJobCurrentNum()), vo.getSubmitToJobNumAIRecommend()));
                    case APPLICATION_COUNT -> excelVo.setSubmitToJobNum("%s（%s）".formatted(safeLong(vo.getSubmitToJobCurrentNum()), vo.getSubmitToJobNumPrecisionAIRecommend()));
                }


            }
        }
        return excelVo;
    }

    private static String safeLong(Long num) {
        return Optional.ofNullable(num).orElse(0L).toString();
    }

}
